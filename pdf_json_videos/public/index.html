<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Processor Tool</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f7fafc;
        }
        .json-output {
            background-color: #1a202c;
            color: #e2e8f0;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-brain mr-3"></i>
                        Question Processor Tool
                    </h1>
                    <p class="text-blue-100 mt-2">Transform questions into structured JSON using AI</p>
                </div>
                <div class="text-right">
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-robot mr-2"></i>
                        Powered by Gemini AI
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Upload Section -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg card-shadow p-8 mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-upload mr-3 text-blue-500"></i>
                    Upload Questions File
                </h2>
                
                <div id="uploadArea" class="upload-area rounded-lg p-8 text-center cursor-pointer">
                    <div id="uploadContent">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg text-gray-600 mb-2">Drop your file here or click to browse</p>
                        <p class="text-sm text-gray-500">Supported formats: .txt, .md, .json (Max 10MB)</p>
                    </div>
                    <input type="file" id="fileInput" class="hidden" accept=".txt,.md,.json">
                </div>

                <div id="fileInfo" class="hidden mt-4 p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                            <span id="fileName" class="font-medium"></span>
                        </div>
                        <button id="removeFile" class="text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <button id="processBtn" class="hidden w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-cogs mr-2"></i>
                    Process Questions
                </button>
            </div>

            <!-- Loading Section -->
            <div id="loadingSection" class="hidden bg-white rounded-lg card-shadow p-8 mb-8">
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Processing Questions...</h3>
                    <p class="text-gray-600">Our AI is analyzing and structuring your questions. This may take a moment.</p>
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="hidden bg-white rounded-lg card-shadow p-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-check-circle mr-3 text-green-500"></i>
                        Processing Complete
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2 text-sm">📋 Questions File</h4>
                            <div class="flex space-x-2">
                                <button id="copyQuestionsBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 text-sm rounded transition duration-300 flex items-center">
                                    <i class="fas fa-copy mr-1"></i>
                                    Copy
                                </button>
                                <button id="downloadQuestionsBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 text-sm rounded transition duration-300 flex items-center">
                                    <i class="fas fa-download mr-1"></i>
                                    Download
                                </button>
                            </div>
                        </div>

                        <div class="bg-purple-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2 text-sm">📄 Test Info File</h4>
                            <div class="flex space-x-2">
                                <button id="copyTestInfoBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 text-sm rounded transition duration-300 flex items-center">
                                    <i class="fas fa-copy mr-1"></i>
                                    Copy
                                </button>
                                <button id="downloadTestInfoBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 text-sm rounded transition duration-300 flex items-center">
                                    <i class="fas fa-download mr-1"></i>
                                    Download
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Metadata -->
                <div id="metadata" class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-blue-600" id="questionCount">0</div>
                            <div class="text-sm text-gray-600">Questions Processed</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-gray-800" id="quizTitle">-</div>
                            <div class="text-sm text-gray-600">Quiz Title</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-600" id="processedTime">-</div>
                            <div class="text-sm text-gray-600">Processed At</div>
                        </div>
                    </div>
                </div>

                <!-- JSON Output -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Generated JSON:</label>
                    <textarea id="jsonOutput" class="json-output w-full h-96 p-4 rounded-lg resize-none" readonly></textarea>
                </div>

                <!-- Validation Results -->
                <div id="validationResults" class="hidden">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Validation Results</h3>
                    <div id="validationContent"></div>
                </div>
            </div>

            <!-- Error Section -->
            <div id="errorSection" class="hidden bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center mb-3">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                    <h3 class="text-lg font-semibold text-red-800">Error Processing File</h3>
                </div>
                <p id="errorMessage" class="text-red-700"></p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 Question Processor Tool. Powered by Gemini AI.</p>
        </div>
    </footer>

    <script>
        class QuestionProcessorUI {
            constructor() {
                this.initializeElements();
                this.attachEventListeners();
                this.selectedFile = null;
            }

            initializeElements() {
                this.uploadArea = document.getElementById('uploadArea');
                this.fileInput = document.getElementById('fileInput');
                this.fileInfo = document.getElementById('fileInfo');
                this.fileName = document.getElementById('fileName');
                this.removeFileBtn = document.getElementById('removeFile');
                this.processBtn = document.getElementById('processBtn');
                this.loadingSection = document.getElementById('loadingSection');
                this.resultsSection = document.getElementById('resultsSection');
                this.errorSection = document.getElementById('errorSection');
                this.jsonOutput = document.getElementById('jsonOutput');
                this.copyQuestionsBtn = document.getElementById('copyQuestionsBtn');
                this.downloadQuestionsBtn = document.getElementById('downloadQuestionsBtn');
                this.copyTestInfoBtn = document.getElementById('copyTestInfoBtn');
                this.downloadTestInfoBtn = document.getElementById('downloadTestInfoBtn');
                this.questionCount = document.getElementById('questionCount');
                this.quizTitle = document.getElementById('quizTitle');
                this.processedTime = document.getElementById('processedTime');
                this.errorMessage = document.getElementById('errorMessage');
                this.validationResults = document.getElementById('validationResults');
                this.validationContent = document.getElementById('validationContent');

                // Store processed data
                this.questionsData = null;
                this.testInfoData = null;
                this.filesInfo = null;
            }

            attachEventListeners() {
                // File upload events
                this.uploadArea.addEventListener('click', () => this.fileInput.click());
                this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                this.removeFileBtn.addEventListener('click', this.removeFile.bind(this));
                this.processBtn.addEventListener('click', this.processFile.bind(this));

                // Action buttons
                this.copyQuestionsBtn.addEventListener('click', this.copyQuestionsToClipboard.bind(this));
                this.downloadQuestionsBtn.addEventListener('click', this.downloadQuestions.bind(this));
                this.copyTestInfoBtn.addEventListener('click', this.copyTestInfoToClipboard.bind(this));
                this.downloadTestInfoBtn.addEventListener('click', this.downloadTestInfo.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                this.uploadArea.classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.selectFile(files[0]);
                }
            }

            handleFileSelect(e) {
                const files = e.target.files;
                if (files.length > 0) {
                    this.selectFile(files[0]);
                }
            }

            selectFile(file) {
                // Validate file type
                const allowedTypes = ['.txt', '.md', '.json'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                if (!allowedTypes.includes(fileExtension)) {
                    this.showError(`Unsupported file type: ${fileExtension}. Allowed types: ${allowedTypes.join(', ')}`);
                    return;
                }

                // Validate file size (10MB)
                if (file.size > 10 * 1024 * 1024) {
                    this.showError('File too large. Maximum size is 10MB.');
                    return;
                }

                this.selectedFile = file;
                this.fileName.textContent = file.name;
                this.fileInfo.classList.remove('hidden');
                this.processBtn.classList.remove('hidden');
                this.hideResults();
            }

            removeFile() {
                this.selectedFile = null;
                this.fileInput.value = '';
                this.fileInfo.classList.add('hidden');
                this.processBtn.classList.add('hidden');
                this.hideResults();
            }

            async processFile() {
                if (!this.selectedFile) return;

                this.showLoading();

                const formData = new FormData();
                formData.append('file', this.selectedFile);

                try {
                    const response = await fetch('/api/process-questions', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showResults(result);
                    } else {
                        this.showError(result.error || 'Failed to process questions');
                    }
                } catch (error) {
                    console.error('Error processing file:', error);
                    this.showError('Network error. Please try again.');
                } finally {
                    this.hideLoading();
                }
            }

            showLoading() {
                this.loadingSection.classList.remove('hidden');
                this.resultsSection.classList.add('hidden');
                this.errorSection.classList.add('hidden');
            }

            hideLoading() {
                this.loadingSection.classList.add('hidden');
            }

            showResults(result) {
                const { data, validation, testInfo, files } = result;

                // Store data for separate downloads
                this.questionsData = data.questions;
                this.testInfoData = testInfo;
                this.filesInfo = files;

                // Update metadata
                this.questionCount.textContent = data.metadata.totalQuestions;
                this.quizTitle.textContent = data.metadata.title;
                this.processedTime.textContent = new Date(data.metadata.processedAt).toLocaleString();

                // Update JSON output (show questions by default)
                this.jsonOutput.value = JSON.stringify(data.questions, null, 2);

                // Show validation results
                if (validation) {
                    this.showValidation(validation);
                }

                this.resultsSection.classList.remove('hidden');
                this.errorSection.classList.add('hidden');
            }

            showValidation(validation) {
                if (!validation.isValid || validation.warnings.length > 0) {
                    let html = '';

                    if (validation.errors.length > 0) {
                        html += '<div class="mb-4"><h4 class="font-semibold text-red-600 mb-2">Errors:</h4><ul class="list-disc list-inside text-red-600 space-y-1">';
                        validation.errors.forEach(error => {
                            html += `<li>${error}</li>`;
                        });
                        html += '</ul></div>';
                    }

                    if (validation.warnings.length > 0) {
                        html += '<div><h4 class="font-semibold text-yellow-600 mb-2">Warnings:</h4><ul class="list-disc list-inside text-yellow-600 space-y-1">';
                        validation.warnings.forEach(warning => {
                            html += `<li>${warning}</li>`;
                        });
                        html += '</ul></div>';
                    }

                    this.validationContent.innerHTML = html;
                    this.validationResults.classList.remove('hidden');
                } else {
                    this.validationResults.classList.add('hidden');
                }
            }

            showError(message) {
                this.errorMessage.textContent = message;
                this.errorSection.classList.remove('hidden');
                this.resultsSection.classList.add('hidden');
            }

            hideResults() {
                this.resultsSection.classList.add('hidden');
                this.errorSection.classList.add('hidden');
            }

            async copyQuestionsToClipboard() {
                if (!this.questionsData) return;
                try {
                    const jsonString = JSON.stringify(this.questionsData, null, 2);
                    await navigator.clipboard.writeText(jsonString);
                    this.showTemporaryMessage(this.copyQuestionsBtn, 'Copied!', 'fas fa-check');
                } catch (error) {
                    console.error('Failed to copy:', error);
                }
            }

            async copyTestInfoToClipboard() {
                if (!this.testInfoData) return;
                try {
                    const jsonString = JSON.stringify(this.testInfoData, null, 2);
                    await navigator.clipboard.writeText(jsonString);
                    this.showTemporaryMessage(this.copyTestInfoBtn, 'Copied!', 'fas fa-check');
                } catch (error) {
                    console.error('Failed to copy:', error);
                }
            }

            downloadQuestions() {
                if (!this.questionsData) return;
                const data = JSON.stringify(this.questionsData, null, 2);
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'questions.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showTemporaryMessage(this.downloadQuestionsBtn, 'Downloaded!', 'fas fa-check');
            }

            downloadTestInfo() {
                if (!this.testInfoData) return;
                const data = JSON.stringify(this.testInfoData, null, 2);
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'test-info.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showTemporaryMessage(this.downloadTestInfoBtn, 'Downloaded!', 'fas fa-check');
            }

            showTemporaryMessage(button, message, iconClass) {
                const originalHTML = button.innerHTML;
                button.innerHTML = `<i class="${iconClass} mr-2"></i>${message}`;
                button.disabled = true;

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.disabled = false;
                }, 2000);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new QuestionProcessorUI();
        });
    </script>
</body>
</html>
